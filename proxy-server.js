const express = require('express');
const cors = require('cors');
const axios = require('axios');

const app = express();
const PORT = 3002;

// 英国服务器SDK API配置
const UK_API_URL = 'http://*************:5001';
const API_KEY = 'wp_china_2024_aigc_detector_key_v1';

// 中间件
app.use(cors({
    origin: ['https://writerpro.cn', 'http://localhost:3000'],
    credentials: true
}));
app.use(express.json({ limit: '10mb' }));

// 健康检查
app.get('/health', async (req, res) => {
    try {
        const response = await fetch(`${UK_API_URL}/health`);
        const data = await response.json();
        res.json(data);
    } catch (error) {
        res.status(500).json({
            error: 'Proxy Error',
            message: error.message
        });
    }
});

// 文本检测
app.post('/detect', async (req, res) => {
    try {
        const response = await fetch(`${UK_API_URL}/detect`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-API-Key': API_KEY
            },
            body: JSON.stringify(req.body)
        });
        
        const data = await response.json();
        res.status(response.status).json(data);
    } catch (error) {
        res.status(500).json({
            error: 'Proxy Error',
            message: error.message
        });
    }
});

// 批量检测
app.post('/batch_detect', async (req, res) => {
    try {
        const response = await fetch(`${UK_API_URL}/batch_detect`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-API-Key': API_KEY
            },
            body: JSON.stringify(req.body)
        });
        
        const data = await response.json();
        res.status(response.status).json(data);
    } catch (error) {
        res.status(500).json({
            error: 'Proxy Error',
            message: error.message
        });
    }
});

app.listen(PORT, () => {
    console.log(`AIGC Proxy Server running on port ${PORT}`);
    console.log(`Forwarding requests to: ${UK_API_URL}`);
});
