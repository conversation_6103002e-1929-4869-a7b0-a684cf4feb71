<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI文本检测与优化工具 - WriterPro</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f7fafc;
            min-height: 100vh;
            color: #333;
        }

        .navbar {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2d3748;
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
            margin: 0;
            padding: 0;
            align-items: center;
        }

        .nav-links a {
            text-decoration: none;
            color: #4a5568;
            font-weight: 500;
            transition: color 0.2s;
            padding: 8px 16px;
            border-radius: 6px;
        }

        .nav-links a:hover {
            color: #4299e1;
            background: rgba(66, 153, 225, 0.1);
        }

        .free-check-btn {
            background: linear-gradient(135deg, #48bb78, #38a169) !important;
            color: white !important;
            padding: 8px 16px !important;
            border-radius: 20px !important;
            font-weight: 600 !important;
            display: flex !important;
            align-items: center !important;
            gap: 6px !important;
            box-shadow: 0 2px 8px rgba(72, 187, 120, 0.3) !important;
        }

        .free-check-btn:hover {
            background: linear-gradient(135deg, #38a169, #2f855a) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(72, 187, 120, 0.4) !important;
            color: white !important;
        }

        .free-check-btn i {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .page-header h1 {
            font-size: 2.5rem;
            color: #2d3748;
            margin-bottom: 1rem;
        }

        .page-header p {
            font-size: 1.125rem;
            color: #718096;
            max-width: 600px;
            margin: 0 auto;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 60px;
        }

        .content-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .text-editor {
            width: 100%;
            min-height: 200px;
            padding: 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            line-height: 1.6;
            resize: vertical;
            font-family: inherit;
            margin-bottom: 16px;
        }

        .text-editor:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .action-bar {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(66, 153, 225, 0.3);
        }

        .btn-secondary {
            background: #f7fafc;
            color: #4a5568;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
        }

        .detector-widget {
            background: #f7fafc;
            border-radius: 8px;
            padding: 20px;
        }

        .detector-header {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .detector-desc {
            color: #718096;
            margin-bottom: 16px;
            font-size: 0.9rem;
        }

        .detector-input {
            width: 100%;
            min-height: 120px;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
            font-family: inherit;
            margin-bottom: 12px;
        }

        .detector-input:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .detector-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .char-count {
            color: #718096;
            font-size: 0.875rem;
        }

        .detect-button {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .detect-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(66, 153, 225, 0.3);
        }

        .detect-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #718096;
        }

        .spinner {
            width: 30px;
            height: 30px;
            border: 3px solid #e2e8f0;
            border-top: 3px solid #4299e1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 12px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message, .success-message {
            padding: 12px;
            border-radius: 6px;
            margin: 12px 0;
            display: none;
            font-size: 0.875rem;
        }

        .error-message {
            background: #fed7d7;
            color: #c53030;
            border: 1px solid #feb2b2;
        }

        .success-message {
            background: #c6f6d5;
            color: #2f855a;
            border: 1px solid #9ae6b4;
        }

        .results-section {
            margin-top: 16px;
        }

        .result-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: none;
        }

        .full-width-section {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .feature-card {
            text-align: center;
            padding: 20px;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #4299e1, #3182ce);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: white;
            font-size: 24px;
        }

        .feature-card h3 {
            font-size: 1.125rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .feature-card p {
            color: #718096;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .page-header h1 {
                font-size: 2rem;
            }
            
            .action-bar {
                flex-direction: column;
            }
            
            .btn {
                justify-content: center;
            }
            
            .detector-meta {
                flex-direction: column;
                gap: 12px;
                align-items: stretch;
            }
            
            .detect-button {
                justify-content: center;
                width: 100%;
            }
            
            .nav-links {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="https://writerpro.cn" class="logo">WriterPro</a>
            <ul class="nav-links">
                <li><a href="https://writerpro.cn/index.html">首页</a></li>
                <li><a href="https://writerpro.cn/history.html">历史记录</a></li>
                <li><a href="https://writerpro.cn/partnership.html">招商加盟</a></li>
                <li><a href="#" class="free-check-btn">
                    <i class="fas fa-gift"></i> 查重永久免费
                </a></li>
            </ul>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>AI文本检测与优化工具</h1>
            <p>专业的AI生成内容检测，帮助您确保文本的原创性和质量</p>
        </div>

        <!-- 主要功能区域 -->
        <div class="content-grid">
            <!-- 文本编辑区 -->
            <div class="content-section">
                <h2 class="section-title">
                    <i class="fas fa-edit"></i> 文本编辑器
                </h2>
                <textarea
                    id="mainTextEditor"
                    class="text-editor"
                    placeholder="在此输入或粘贴您的文本内容...&#10;&#10;支持功能：&#10;• AI内容检测&#10;• 文本优化建议&#10;• 原创性分析"
                ></textarea>
                <div class="action-bar">
                    <button class="btn btn-primary" onclick="copyToDetector()">
                        <i class="fas fa-arrow-right"></i> 检测AI内容
                    </button>
                    <button class="btn btn-secondary" onclick="optimizeText()">
                        <i class="fas fa-magic"></i> 优化文本
                    </button>
                </div>
            </div>

            <!-- AI检测区 -->
            <div class="content-section">
                <h2 class="section-title">
                    <i class="fas fa-robot"></i> AI内容检测
                </h2>
                <div class="detector-widget">
                    <div class="detector-header">
                        <i class="fas fa-search"></i> AI文本检测
                    </div>
                    <p class="detector-desc">检测文本是否为AI生成</p>
                    <textarea
                        id="textInput"
                        class="detector-input"
                        placeholder="输入需要检测的文本..."
                    ></textarea>
                    <div class="detector-meta">
                        <span class="char-count">字符: <span id="charCount">0</span></span>
                        <button id="detectBtn" class="detect-button">
                            <i class="fas fa-search"></i> 检测
                        </button>
                    </div>

                    <div class="loading" id="loading">
                        <div class="spinner"></div>
                        <p>正在分析文本内容，请稍候...</p>
                    </div>

                    <div class="error-message" id="errorMessage"></div>
                    <div class="success-message" id="successMessage"></div>

                    <div class="results-section" id="resultsSection">
                        <div class="result-card" id="resultCard">
                            <!-- 检测结果将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能介绍 -->
        <div class="full-width-section">
            <h2 class="section-title">
                <i class="fas fa-star"></i> 核心功能
            </h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>AI内容检测</h3>
                    <p>使用先进的深度学习模型，准确识别AI生成的文本内容</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-language"></i>
                    </div>
                    <h3>中英文支持</h3>
                    <p>支持中文和英文文本检测，自动识别语言类型</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>详细分析</h3>
                    <p>提供详细的概率分析和改进建议</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>隐私保护</h3>
                    <p>本地处理，保护您的文本内容隐私安全</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API配置 - 使用PHP代理避免HTTPS/HTTP混合内容问题
        const API_BASE_URL = 'api.php';

        // DOM元素
        const mainTextEditor = document.getElementById('mainTextEditor');
        const textInput = document.getElementById('textInput');
        const charCount = document.getElementById('charCount');
        const detectBtn = document.getElementById('detectBtn');
        const loading = document.getElementById('loading');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');
        const resultsSection = document.getElementById('resultsSection');
        const resultCard = document.getElementById('resultCard');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 字符计数
            textInput.addEventListener('input', function() {
                const count = textInput.value.length;
                charCount.textContent = count;
            });

            // 检测按钮点击事件
            detectBtn.addEventListener('click', function() {
                const text = textInput.value.trim();
                if (!text) {
                    showError('请输入需要检测的文本内容');
                    return;
                }
                if (text.length < 10) {
                    showError('文本长度至少需要10个字符才能进行准确检测');
                    return;
                }
                detectText(text);
            });

            // 快捷键支持
            textInput.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'Enter') {
                    detectBtn.click();
                }
            });
        });

        // 复制到检测器
        function copyToDetector() {
            const text = mainTextEditor.value.trim();
            if (!text) {
                showError('请先在文本编辑器中输入内容');
                return;
            }
            textInput.value = text;
            charCount.textContent = text.length;
            showSuccess('文本已复制到检测器');
        }

        // 优化文本（占位功能）
        function optimizeText() {
            const text = mainTextEditor.value.trim();
            if (!text) {
                showError('请先在文本编辑器中输入内容');
                return;
            }
            showSuccess('文本优化功能正在开发中，敬请期待！');
        }

        // 检测文本 - 使用前端完整实现
        async function detectText(text) {
            try {
                showLoading(true);
                hideMessages();
                hideResult();

                // 使用前端检测逻辑
                const result = await performAdvancedDetection(text);
                showResult(result);

            } catch (err) {
                console.error('检测错误:', err);
                showError('检测过程中出现错误，请稍后重试。');
            } finally {
                showLoading(false);
            }
        }

        // 高级检测方案
        async function performAdvancedDetection(text) {
            const language = detectLanguage(text);

            // AI指示词检测
            const aiIndicators = [
                // 中文指示词
                "作为一个AI", "作为人工智能", "我是一个AI", "我是人工智能", "根据我的训练",
                "我无法", "我不能", "很抱歉", "我理解您的", "让我来", "我可以帮助",
                "根据提供的信息", "基于我的知识", "我建议", "总的来说", "综上所述",

                // 英文指示词
                "As an AI", "I'm an AI", "I am an AI", "based on my training",
                "I cannot", "I can't", "I apologize", "I understand that",
                "Let me", "I can help", "Based on the information", "In my knowledge",
                "I suggest", "In conclusion", "To summarize"
            ];

            // 文本特征分析
            const features = analyzeTextFeatures(text, language);

            // 计算AI概率
            let aiScore = 0;

            // 1. 指示词检测 (权重: 0.4)
            const textLower = text.toLowerCase();
            let indicatorCount = 0;
            for (const indicator of aiIndicators) {
                if (textLower.includes(indicator.toLowerCase())) {
                    indicatorCount++;
                }
            }
            aiScore += Math.min(indicatorCount * 0.15, 0.4);

            // 2. 文本结构特征 (权重: 0.3)
            aiScore += features.structureScore * 0.3;

            // 3. 语言模式特征 (权重: 0.2)
            aiScore += features.patternScore * 0.2;

            // 4. 长度和复杂度 (权重: 0.1)
            aiScore += features.complexityScore * 0.1;

            // 限制在合理范围内
            const aiProbability = Math.min(Math.max(aiScore, 0.05), 0.95);

            // 确定置信度
            let confidence = 'low';
            if (Math.abs(aiProbability - 0.5) > 0.3) {
                confidence = 'high';
            } else if (Math.abs(aiProbability - 0.5) > 0.15) {
                confidence = 'medium';
            }

            return {
                ai_probability: Math.round(aiProbability * 1000) / 1000,
                human_probability: Math.round((1 - aiProbability) * 1000) / 1000,
                prediction: aiProbability > 0.5 ? 'AI-generated' : 'Human-written',
                confidence: confidence,
                language: language,
                model_used: 'advanced-browser-detection',
                text_length: text.length,
                features: features,
                sdk_version: '2.0.0-advanced',
                timestamp: Date.now() / 1000
            };
        }

        // 文本特征分析
        function analyzeTextFeatures(text, language) {
            const sentences = text.split(/[.。!！?？]+/).filter(s => s.trim().length > 0);
            const words = text.split(/\s+/).filter(w => w.length > 0);

            // 结构特征
            let structureScore = 0;

            // 句子长度一致性 (AI倾向于生成长度相似的句子)
            if (sentences.length > 2) {
                const avgLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;
                const variance = sentences.reduce((sum, s) => sum + Math.pow(s.length - avgLength, 2), 0) / sentences.length;
                const coefficient = Math.sqrt(variance) / avgLength;
                if (coefficient < 0.3) structureScore += 0.3; // 长度过于一致
            }

            // 段落结构 (AI倾向于规整的段落)
            const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
            if (paragraphs.length > 1) {
                const avgParaLength = paragraphs.reduce((sum, p) => sum + p.length, 0) / paragraphs.length;
                const paraVariance = paragraphs.reduce((sum, p) => sum + Math.pow(p.length - avgParaLength, 2), 0) / paragraphs.length;
                const paraCoeff = Math.sqrt(paraVariance) / avgParaLength;
                if (paraCoeff < 0.4) structureScore += 0.2;
            }

            // 语言模式特征
            let patternScore = 0;

            // 连接词使用频率 (AI倾向于使用更多连接词)
            const connectors = language === 'zh' ?
                ['因此', '所以', '然而', '但是', '而且', '另外', '此外', '总之', '首先', '其次', '最后'] :
                ['therefore', 'however', 'moreover', 'furthermore', 'additionally', 'consequently', 'firstly', 'secondly', 'finally'];

            let connectorCount = 0;
            const textLower = text.toLowerCase();
            for (const connector of connectors) {
                if (textLower.includes(connector.toLowerCase())) {
                    connectorCount++;
                }
            }
            patternScore += Math.min(connectorCount / sentences.length * 2, 0.4);

            // 重复模式检测
            const phrases = [];
            for (let i = 0; i < words.length - 2; i++) {
                phrases.push(words.slice(i, i + 3).join(' '));
            }
            const uniquePhrases = new Set(phrases);
            const repetitionRate = 1 - (uniquePhrases.size / phrases.length);
            if (repetitionRate > 0.1) patternScore += 0.2;

            // 复杂度特征
            let complexityScore = 0;

            // 词汇多样性
            const uniqueWords = new Set(words.map(w => w.toLowerCase()));
            const lexicalDiversity = uniqueWords.size / words.length;
            if (lexicalDiversity < 0.6) complexityScore += 0.3; // AI倾向于词汇重复

            // 句子复杂度
            const avgWordsPerSentence = words.length / sentences.length;
            if (avgWordsPerSentence > 15 && avgWordsPerSentence < 25) {
                complexityScore += 0.2; // AI倾向于中等长度句子
            }

            return {
                structureScore: Math.min(structureScore, 1),
                patternScore: Math.min(patternScore, 1),
                complexityScore: Math.min(complexityScore, 1),
                sentenceCount: sentences.length,
                wordCount: words.length,
                avgSentenceLength: sentences.length > 0 ? words.length / sentences.length : 0,
                lexicalDiversity: lexicalDiversity
            };
        }

        // 语言检测
        function detectLanguage(text) {
            const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
            const totalChars = text.replace(/\s/g, '').length;

            if (totalChars === 0) return 'en';

            const chineseRatio = chineseChars / totalChars;
            return chineseRatio > 0.3 ? 'zh' : 'en';
        }

        // 显示结果
        function showResult(data) {
            const aiProbability = data.ai_probability || 0;
            const isAI = aiProbability > 0.5;

            const iconClass = isAI ? 'fas fa-robot' : 'fas fa-user';
            const statusText = isAI ? 'AI生成内容' : '人工创作内容';
            const description = isAI ?
                '检测结果显示该文本疑似由AI生成，建议进一步核实' :
                '检测结果显示该文本疑似为人工创作，原创性较高';

            const confidence = data.confidence || 'medium';
            const confidenceText = {
                'high': '高',
                'medium': '中等',
                'low': '低'
            }[confidence] || '中等';

            resultCard.innerHTML = `
                <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 20px;">
                    <div style="width: 50px; height: 50px; border-radius: 50%; background: ${isAI ? 'linear-gradient(135deg, #f56565, #e53e3e)' : 'linear-gradient(135deg, #48bb78, #38a169)'}; display: flex; align-items: center; justify-content: center; color: white; font-size: 20px;">
                        <i class="${iconClass}"></i>
                    </div>
                    <div>
                        <div style="font-size: 1.25rem; font-weight: 600; color: #2d3748; margin-bottom: 4px;">${statusText}</div>
                        <div style="color: #718096;">${description}</div>
                    </div>
                </div>

                <div style="background: #f7fafc; border-radius: 8px; padding: 20px; text-align: center; margin: 16px 0;">
                    <div style="font-size: 2rem; font-weight: 700; color: #2d3748; margin-bottom: 4px;">${(aiProbability * 100).toFixed(1)}%</div>
                    <div style="color: #718096;">AI生成概率</div>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 12px; margin-top: 20px;">
                    <div style="background: #f7fafc; padding: 12px; border-radius: 6px; text-align: center;">
                        <div style="color: #718096; font-size: 0.875rem; margin-bottom: 4px;">检测模型</div>
                        <div style="color: #2d3748; font-weight: 600; font-size: 0.875rem;">${data.model_used ? (data.model_used.includes('zhv2') ? '中文模型' : '英文模型') : '未知'}</div>
                    </div>
                    <div style="background: #f7fafc; padding: 12px; border-radius: 6px; text-align: center;">
                        <div style="color: #718096; font-size: 0.875rem; margin-bottom: 4px;">文本语言</div>
                        <div style="color: #2d3748; font-weight: 600; font-size: 0.875rem;">${data.language === 'zh' ? '中文' : data.language === 'en' ? '英文' : '未知'}</div>
                    </div>
                    <div style="background: #f7fafc; padding: 12px; border-radius: 6px; text-align: center;">
                        <div style="color: #718096; font-size: 0.875rem; margin-bottom: 4px;">文本长度</div>
                        <div style="color: #2d3748; font-weight: 600; font-size: 0.875rem;">${data.text_length || 0} 字符</div>
                    </div>
                    <div style="background: #f7fafc; padding: 12px; border-radius: 6px; text-align: center;">
                        <div style="color: #718096; font-size: 0.875rem; margin-bottom: 4px;">检测置信度</div>
                        <div style="color: #2d3748; font-weight: 600; font-size: 0.875rem;">${confidenceText}</div>
                    </div>
                </div>
            `;

            resultCard.style.display = 'block';
            resultsSection.style.display = 'block';
        }

        // 显示加载状态
        function showLoading(show) {
            loading.style.display = show ? 'block' : 'none';
            detectBtn.disabled = show;
        }

        // 显示错误信息
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';
        }

        // 显示成功信息
        function showSuccess(message) {
            successMessage.textContent = message;
            successMessage.style.display = 'block';
            errorMessage.style.display = 'none';
        }

        // 隐藏消息
        function hideMessages() {
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
        }

        // 隐藏结果
        function hideResult() {
            resultsSection.style.display = 'none';
            resultCard.style.display = 'none';
        }
    </script>
</body>
</html>
