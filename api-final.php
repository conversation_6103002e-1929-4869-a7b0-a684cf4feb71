<?php
// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 错误处理函数
function sendError($message, $code = 500) {
    http_response_code($code);
    echo json_encode([
        'error' => true,
        'message' => $message,
        'timestamp' => time()
    ]);
    exit();
}

// SDK API配置
$SDK_API_URL = 'http://92.112.125.16:5001';
$API_KEY = 'wp_china_2024_aigc_detector_key_v1';

// 获取请求路径
$path = '/detect'; // 默认路径
if (isset($_GET['path'])) {
    $path = $_GET['path'];
} elseif (isset($_SERVER['PATH_INFO'])) {
    $path = $_SERVER['PATH_INFO'];
}

// 确保路径格式正确
if (substr($path, 0, 1) !== '/') {
    $path = '/' . $path;
}

// 构建API URL
$api_url = $SDK_API_URL . $path;

// 获取请求方法和数据
$method = $_SERVER['REQUEST_METHOD'];
$post_data = null;

if ($method === 'POST') {
    $post_data = file_get_contents('php://input');
    if (empty($post_data)) {
        sendError('请求数据为空', 400);
    }
    
    // 验证JSON格式
    $json_data = json_decode($post_data, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        sendError('请求数据格式错误', 400);
    }
    
    // 验证必要字段
    if (!isset($json_data['text']) || empty(trim($json_data['text']))) {
        sendError('文本内容不能为空', 400);
    }
}

// 初始化cURL
$ch = curl_init();
if (!$ch) {
    sendError('无法初始化HTTP客户端');
}

// 设置cURL选项
curl_setopt_array($ch, [
    CURLOPT_URL => $api_url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 60,
    CURLOPT_CONNECTTIMEOUT => 10,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false
]);

// 设置请求头
$headers = [];

if ($method === 'POST') {
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
    $headers[] = 'Content-Type: application/json';
    $headers[] = 'Content-Length: ' . strlen($post_data);
    $headers[] = 'X-API-Key: ' . $API_KEY;
} elseif ($path !== '/health') {
    // GET请求，非health检查需要API密钥
    $headers[] = 'X-API-Key: ' . $API_KEY;
}

if (!empty($headers)) {
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
}

// 执行请求
$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);

curl_close($ch);

// 处理cURL错误
if ($curl_error) {
    sendError('网络请求失败: ' . $curl_error);
}

// 处理HTTP错误
if ($http_code === 0) {
    sendError('无法连接到检测服务');
}

// 验证响应
if (empty($response)) {
    sendError('检测服务返回空响应');
}

// 验证JSON响应
$json_response = json_decode($response, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    // 如果不是JSON，可能是错误信息，直接返回
    http_response_code($http_code);
    echo $response;
    exit();
}

// 设置响应状态码并返回结果
http_response_code($http_code);
echo $response;
?>
